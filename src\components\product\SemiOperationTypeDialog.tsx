import { Dialog, DialogTrigger, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import React from 'react';

interface OperationTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (type: 'Entrée' | 'Sortie' | 'Complément Stock', mode?: 'local' | 'ready') => void;
  trigger?: React.ReactNode;
}

const SemiOperationTypeDialog: React.FC<OperationTypeDialogProps> = ({ open, onOpenChange, onSelect, trigger }) => {
  const [step, setStep] = React.useState(0);

  React.useEffect(() => {
    if (!open) setStep(0);
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Nouvelle opération</DialogTitle>
        </DialogHeader>
        {step === 0 ? (
          <div className="flex flex-col gap-4 py-4">
            <Button size="lg" variant="default" onClick={() => setStep(1)}>Entrée</Button>
            <Button size="lg" variant="secondary" onClick={() => { onSelect('Sortie'); onOpenChange(false); }}>Sortie</Button>
          </div>
        ) : (
          <div className="flex flex-col gap-4 py-4">
            <div className="mb-2 font-medium">Type d'entrée :</div>
            <Button size="lg" variant="default" onClick={() => onSelect('Entrée', 'local')}>Nouveau produit</Button>
            <Button size="lg" variant="secondary" onClick={() => onSelect('Entrée', 'ready')}>Prêt à l'emploi</Button>
            <Button size="lg" variant="secondary" onClick={() => onSelect('Complément Stock')}>Complément Stock</Button>
            <Button variant="ghost" onClick={() => setStep(0)}>Retour</Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default SemiOperationTypeDialog; 